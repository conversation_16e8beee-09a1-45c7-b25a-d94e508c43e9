<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini Voice Selection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e293b;
            color: #f1f5f9;
        }
        .container {
            background-color: #334155;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        select {
            width: 100%;
            padding: 8px;
            background-color: #475569;
            color: #f1f5f9;
            border: 1px solid #64748b;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .voice-info {
            background-color: #475569;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Gemini TTS Voice Selection Test</h1>

    <div class="container">
        <h2>TTS Service Selection</h2>
        <select id="ttsService">
            <option value="">None</option>
            <option value="gemini">Gemini TTS (Conceptual)</option>
            <option value="other">Other TTS Service</option>
        </select>

        <div id="voiceSelection" class="hidden">
            <h3>Gemini Voice Type</h3>
            <select id="geminiVoice">
                <option value="">Select voice type...</option>
                <optgroup label="Nam (Male)">
                    <option value="Puck">Puck - Vui nhộn - Content viral, TikTok, intro vui (Viral content, TikTok, fun intro)</option>
                    <option value="Charon">Charon - Thông tin rõ ràng - Blog, học tập, bản tin (Blog, learning, news)</option>
                    <option value="Fenrir">Fenrir - Giọng phấn khích, sôi nổi - Sự kiện, khuyến mãi, video hype (Events, promotions, hype videos)</option>
                    <option value="Orus">Orus - Dứt khoát, nghiêm túc, uy tín - B2B, giới thiệu sản phẩm công nghệ, video chính sự kiện (B2B, tech product intro, official event videos)</option>
                </optgroup>
                <optgroup label="Nữ (Female)">
                    <option value="Zephyr">Zephyr - Sáng sủa, tươi tắn, tích cực - Truyền cảm hứng, giới thiệu nhẹ nhàng (Inspirational, gentle introduction)</option>
                    <option value="Kore">Kore - Giọng chắc chắn, nghiêm túc - Kể chuyện sâu, dự án chuyên nghiệp (Deep storytelling, professional projects)</option>
                    <option value="Leda">Leda - Trẻ trung, sôi nổi, có chút tinh nghịch - Game, sản phẩm dành cho Gen Z, clip vui (Gaming, Gen Z products, fun clips)</option>
                    <option value="Aoede">Aoede - Thoải mái, nhẹ nhàng - Du lịch, lifestyle, vlog tự nhiên (Travel, lifestyle, natural vlogs)</option>
                </optgroup>
            </select>

            <div id="voiceInfo" class="voice-info hidden">
                <strong>Purpose:</strong> <span id="voicePurpose"></span>
            </div>
        </div>
    </div>

    <script>
        const voices = {
            'Puck': 'Content viral, TikTok, intro vui (Viral content, TikTok, fun intro)',
            'Charon': 'Blog, học tập, bản tin (Blog, learning, news)',
            'Fenrir': 'Sự kiện, khuyến mãi, video hype (Events, promotions, hype videos)',
            'Orus': 'B2B, giới thiệu sản phẩm công nghệ, video chính sự kiện (B2B, tech product intro, official event videos)',
            'Zephyr': 'Truyền cảm hứng, giới thiệu nhẹ nhàng (Inspirational, gentle introduction)',
            'Kore': 'Kể chuyện sâu, dự án chuyên nghiệp (Deep storytelling, professional projects)',
            'Leda': 'Game, sản phẩm dành cho Gen Z, clip vui (Gaming, Gen Z products, fun clips)',
            'Aoede': 'Du lịch, lifestyle, vlog tự nhiên (Travel, lifestyle, natural vlogs)'
        };

        document.getElementById('ttsService').addEventListener('change', function() {
            const voiceSelection = document.getElementById('voiceSelection');
            if (this.value === 'gemini') {
                voiceSelection.classList.remove('hidden');
            } else {
                voiceSelection.classList.add('hidden');
                document.getElementById('geminiVoice').value = '';
                document.getElementById('voiceInfo').classList.add('hidden');
            }
        });

        document.getElementById('geminiVoice').addEventListener('change', function() {
            const voiceInfo = document.getElementById('voiceInfo');
            const voicePurpose = document.getElementById('voicePurpose');

            if (this.value && voices[this.value]) {
                voicePurpose.textContent = voices[this.value];
                voiceInfo.classList.remove('hidden');
            } else {
                voiceInfo.classList.add('hidden');
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini TTS Additional Prompt Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e293b;
            color: #f1f5f9;
        }
        .container {
            background-color: #334155;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        select, textarea {
            width: 100%;
            padding: 8px;
            background-color: #475569;
            color: #f1f5f9;
            border: 1px solid #64748b;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        textarea {
            min-height: 80px;
            resize: vertical;
        }
        .voice-info {
            background-color: #475569;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .hidden {
            display: none;
        }
        .prompt-examples {
            background-color: #1e293b;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Gemini TTS Additional Prompt Test</h1>
    
    <div class="container">
        <h2>TTS Service Selection</h2>
        <select id="ttsService">
            <option value="">None</option>
            <option value="gemini">Gemini TTS (Conceptual)</option>
            <option value="other">Other TTS Service</option>
        </select>
        
        <div id="geminiOptions" class="hidden">
            <h3>Gemini Voice Type</h3>
            <select id="geminiVoice">
                <option value="">Select voice type...</option>
                <optgroup label="Nam (Male)">
                    <option value="Puck">Puck - Vui nhộn - Content viral, TikTok, intro vui</option>
                    <option value="Charon">Charon - Thông tin rõ ràng - Blog, học tập, bản tin</option>
                </optgroup>
                <optgroup label="Nữ (Female)">
                    <option value="Zephyr">Zephyr - Sáng sủa, tươi tắn, tích cực - Truyền cảm hứng</option>
                    <option value="Kore">Kore - Giọng chắc chắn, nghiêm túc - Kể chuyện sâu</option>
                </optgroup>
            </select>
            
            <div id="voiceInfo" class="voice-info hidden">
                <strong>Purpose:</strong> <span id="voicePurpose"></span>
            </div>
            
            <h3>Additional Prompt (Optional)</h3>
            <textarea 
                id="additionalPrompt" 
                placeholder="Enter additional instructions for TTS generation (e.g., speaking style, tone, emphasis)..."
                rows="3"
            ></textarea>
            
            <div class="prompt-examples">
                <strong>Example prompts:</strong><br>
                • "Speak slowly and clearly"<br>
                • "Emphasize technical terms"<br>
                • "Use a conversational tone"<br>
                • "Pause between sentences for clarity"<br>
                • "Speak with enthusiasm and energy"
            </div>
        </div>
    </div>

    <div class="container">
        <h2>API Payload Preview</h2>
        <pre id="apiPayload" style="background-color: #1e293b; padding: 10px; border-radius: 4px; overflow-x: auto;">
Select Gemini TTS to see payload...
        </pre>
    </div>

    <script>
        const voices = {
            'Puck': 'Content viral, TikTok, intro vui',
            'Charon': 'Blog, học tập, bản tin',
            'Zephyr': 'Truyền cảm hứng, giới thiệu nhẹ nhàng',
            'Kore': 'Kể chuyện sâu, dự án chuyên nghiệp'
        };

        function updatePayload() {
            const ttsService = document.getElementById('ttsService').value;
            const geminiVoice = document.getElementById('geminiVoice').value;
            const additionalPrompt = document.getElementById('additionalPrompt').value;
            
            if (ttsService === 'gemini') {
                const payload = {
                    ttsService: "gemini",
                    ttsLanguageTarget: "vi",
                    ttsLanguageVoice: geminiVoice || "Puck",
                    additionalPrompt: additionalPrompt || undefined,
                    apiKey: "your-api-key-here"
                };
                
                // Remove undefined values for cleaner display
                Object.keys(payload).forEach(key => 
                    payload[key] === undefined && delete payload[key]
                );
                
                document.getElementById('apiPayload').textContent = 
                    JSON.stringify(payload, null, 2);
            } else {
                document.getElementById('apiPayload').textContent = 
                    'Select Gemini TTS to see payload...';
            }
        }

        document.getElementById('ttsService').addEventListener('change', function() {
            const geminiOptions = document.getElementById('geminiOptions');
            if (this.value === 'gemini') {
                geminiOptions.classList.remove('hidden');
            } else {
                geminiOptions.classList.add('hidden');
                document.getElementById('geminiVoice').value = '';
                document.getElementById('additionalPrompt').value = '';
                document.getElementById('voiceInfo').classList.add('hidden');
            }
            updatePayload();
        });

        document.getElementById('geminiVoice').addEventListener('change', function() {
            const voiceInfo = document.getElementById('voiceInfo');
            const voicePurpose = document.getElementById('voicePurpose');
            
            if (this.value && voices[this.value]) {
                voicePurpose.textContent = voices[this.value];
                voiceInfo.classList.remove('hidden');
            } else {
                voiceInfo.classList.add('hidden');
            }
            updatePayload();
        });

        document.getElementById('additionalPrompt').addEventListener('input', updatePayload);
    </script>
</body>
</html>
